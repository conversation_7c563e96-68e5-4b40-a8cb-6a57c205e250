// 溢价商品筛选配置

// 内置配置 - 直接在代码中配置目标商品
export const PREMIUM_CONFIG = {
  enabled: true,
  maxPremiumPercentage: 0.1, // 10%
  targetTemplates: [
    '蝴蝶刀（★） | 多普勒',
    '蝴蝶刀（★） | 伽玛多普勒',
    '爪子刀（★） | 多普勒',
    '爪子刀（★） | 伽玛多普勒',
    'M9 刺刀（★） | 多普勒',
    'M9 刺刀（★） | 伽玛多普勒',
    '折叠刀（★） | 多普勒',
    '爪子刀（★） | 渐变大理石',
    '蝴蝶刀（★） | 渐变大理石',
    'M9 刺刀（★） | 渐变大理石',
    '专业手套（★） | 渐变之色',
    '专业手套（★） | 深红和服',
    '运动手套（★） | 大型猎物',
    '运动手套（★） | 夜行衣',
    '运动手套（★） | 双栖',
    '运动手套（★） | 迈阿密风云',
    'AK-47 | 表面淬火',
    'AK-47（StatTrak™） | 表面淬火',
    'M4A1 消音型 | 伊卡洛斯殒落',
    '短剑（★） | 多普勒',
    '格洛克 18 型 | 伽玛多普勒',
  ],
  sendNotification: true
};

// 获取配置的辅助函数
export function getPremiumConfig() {
  return PREMIUM_CONFIG;
}

// 检查模板是否在筛选列表中
export function isTemplateTargeted(templateName) {
  const config = getPremiumConfig();
  if (!config.enabled) return false;

  // 支持模糊匹配
  return config.targetTemplates.some(target =>
    templateName.includes(target) || target.includes(templateName)
  );
}

// 计算溢价百分比
export function calculatePremiumPercentage(currentPrice, floorPrice) {
  if (floorPrice <= 0) return 0;
  return (currentPrice - floorPrice) / floorPrice;
}

// 检查是否符合溢价条件
export function isPremiumWithinLimit(currentPrice, floorPrice) {
  const config = getPremiumConfig();
  const premiumPercentage = calculatePremiumPercentage(currentPrice, floorPrice);
  return premiumPercentage >= 0 && premiumPercentage <= config.maxPremiumPercentage;
}